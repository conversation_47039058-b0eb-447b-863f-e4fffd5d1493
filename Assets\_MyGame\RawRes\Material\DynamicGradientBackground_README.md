# 动态渐变背景系统

这个系统提供了一个高性能的动态渐变背景效果，类似于你提供的游戏截图中的粉色到蓝色渐变效果。

## 文件说明

### Shader文件
- `DynamicGradientBackground.shader` - 主要的渐变Shader，支持URP渲染管线
- `DynamicGradientBackground.mat` - 使用该Shader的材质文件

### 脚本文件
- `DynamicGradientController.cs` - 渐变控制器，用于动态调整渐变参数
- `BackgroundManager.cs` - 背景管理器，管理不同场景的背景配置

## 使用方法

### 1. 基本设置

1. 在场景中创建一个Canvas
2. 在Canvas下创建一个Image组件
3. 将`DynamicGradientBackground.mat`材质赋给Image组件
4. 添加`DynamicGradientController`脚本到Image对象上

### 2. 参数说明

#### Shader参数
- **Top Color**: 渐变顶部颜色
- **Bottom Color**: 渐变底部颜色  
- **Animation Speed**: 动画播放速度
- **Wave Amplitude**: 波浪幅度
- **Wave Frequency**: 波浪频率
- **Noise Scale**: 噪声缩放
- **Noise Strength**: 噪声强度

#### 脚本参数
- **Gradient Material**: 渐变材质引用
- **Background Image**: 背景Image组件引用
- **Presets**: 预设配置数组

### 3. 代码示例

```csharp
// 获取背景控制器
DynamicGradientController controller = GetComponent<DynamicGradientController>();

// 设置渐变颜色
controller.SetGradientColors(Color.red, Color.blue);

// 设置动画速度
controller.SetAnimationSpeed(1.0f);

// 平滑过渡到新颜色
controller.TransitionToColors(Color.yellow, Color.green, 2.0f);
```

### 4. 背景管理器使用

```csharp
// 获取背景管理器
BackgroundManager manager = GetComponent<BackgroundManager>();

// 根据游戏状态切换背景
manager.UpdateBackgroundForGameState(GameState.Playing);

// 手动切换场景背景
manager.ApplySceneBackground(1);
```

## 特性

1. **高性能**: 使用GPU计算，对性能影响极小
2. **动态效果**: 支持实时波浪和噪声动画
3. **易于定制**: 丰富的参数可调整各种效果
4. **平滑过渡**: 支持颜色之间的平滑过渡动画
5. **预设系统**: 可以保存和应用不同的配置预设
6. **URP兼容**: 完全支持Universal Render Pipeline

## 效果预览

该系统可以创建以下效果：
- 垂直渐变（如粉色到蓝色）
- 动态波浪扰动
- 微妙的噪声变化
- 时间驱动的颜色偏移
- 亮度变化动画

## 性能优化

- Shader使用HLSL编写，充分利用GPU并行计算
- 材质实例化避免了不必要的Draw Call
- 参数缓存减少了CPU到GPU的数据传输

## 扩展建议

1. 可以添加更多的噪声类型（如Perlin噪声）
2. 支持多层渐变效果
3. 添加粒子效果集成
4. 支持纹理混合模式
