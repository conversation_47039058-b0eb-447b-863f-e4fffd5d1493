using UnityEngine;

namespace MyGame.UI
{
    /// <summary>
    /// 渐变预设管理器
    /// 提供各种预定义的漂亮颜色组合
    /// </summary>
    public class GradientPresetManager : MonoBehaviour
    {
        [Header("预设配置")]
        [SerializeField] private DynamicGradientController gradientController;
        
        [Header("预设列表")]
        [SerializeField] private GradientPreset[] presets;
        
        void Start()
        {
            InitializePresets();
        }
        
        /// <summary>
        /// 初始化预设
        /// </summary>
        private void InitializePresets()
        {
            if (presets == null || presets.Length == 0)
            {
                CreateDefaultPresets();
            }
        }
        
        /// <summary>
        /// 创建默认预设
        /// </summary>
        private void CreateDefaultPresets()
        {
            presets = new GradientPreset[]
            {
                // 粉色梦幻
                new GradientPreset
                {
                    name = "粉色梦幻",
                    color1 = new Color(1f, 0.7f, 0.8f, 1f),      // 浅粉
                    color2 = new Color(1f, 0.5f, 0.7f, 1f),      // 中粉
                    color3 = new Color(0.8f, 0.6f, 1f, 1f),      // 淡紫
                    color4 = new Color(0.5f, 0.8f, 1f, 1f),      // 浅蓝
                    colorCount = 4,
                    animationSpeed = 0.5f,
                    colorShiftSpeed = 0.3f
                },
                
                // 日落黄昏
                new GradientPreset
                {
                    name = "日落黄昏",
                    color1 = new Color(1f, 0.8f, 0.4f, 1f),      // 金黄
                    color2 = new Color(1f, 0.6f, 0.3f, 1f),      // 橙色
                    color3 = new Color(1f, 0.4f, 0.5f, 1f),      // 橙红
                    color4 = new Color(0.6f, 0.3f, 0.8f, 1f),    // 深紫
                    colorCount = 4,
                    animationSpeed = 0.3f,
                    colorShiftSpeed = 0.2f
                },
                
                // 海洋深蓝
                new GradientPreset
                {
                    name = "海洋深蓝",
                    color1 = new Color(0.4f, 0.8f, 1f, 1f),      // 天蓝
                    color2 = new Color(0.3f, 0.6f, 0.9f, 1f),    // 中蓝
                    color3 = new Color(0.2f, 0.4f, 0.8f, 1f),    // 深蓝
                    color4 = new Color(0.1f, 0.2f, 0.6f, 1f),    // 海军蓝
                    colorCount = 4,
                    animationSpeed = 0.4f,
                    waveAmplitude = 0.15f,
                    colorShiftSpeed = 0.25f
                },
                
                // 春日绿意
                new GradientPreset
                {
                    name = "春日绿意",
                    color1 = new Color(0.8f, 1f, 0.6f, 1f),      // 浅绿
                    color2 = new Color(0.6f, 0.9f, 0.7f, 1f),    // 薄荷绿
                    color3 = new Color(0.4f, 0.8f, 0.8f, 1f),    // 青绿
                    color4 = new Color(0.3f, 0.6f, 0.9f, 1f),    // 青蓝
                    colorCount = 4,
                    animationSpeed = 0.6f,
                    colorShiftSpeed = 0.4f
                },
                
                // 紫色幻想
                new GradientPreset
                {
                    name = "紫色幻想",
                    color1 = new Color(0.9f, 0.7f, 1f, 1f),      // 淡紫
                    color2 = new Color(0.7f, 0.5f, 1f, 1f),      // 中紫
                    color3 = new Color(0.5f, 0.3f, 0.9f, 1f),    // 深紫
                    color4 = new Color(0.3f, 0.1f, 0.7f, 1f),    // 暗紫
                    colorCount = 4,
                    animationSpeed = 0.7f,
                    waveFrequency = 4f,
                    colorShiftSpeed = 0.5f
                },
                
                // 简约双色
                new GradientPreset
                {
                    name = "简约双色",
                    color1 = new Color(1f, 0.9f, 0.9f, 1f),      // 浅粉白
                    color4 = new Color(0.7f, 0.9f, 1f, 1f),      // 浅蓝
                    colorCount = 2,
                    animationSpeed = 0.3f,
                    waveAmplitude = 0.05f,
                    colorShiftSpeed = 0.1f
                }
            };
        }
        
        /// <summary>
        /// 应用指定预设
        /// </summary>
        public void ApplyPreset(int index)
        {
            if (gradientController != null && presets != null && index >= 0 && index < presets.Length)
            {
                gradientController.ApplyPreset(index);
            }
        }
        
        /// <summary>
        /// 应用指定预设（按名称）
        /// </summary>
        public void ApplyPreset(string presetName)
        {
            if (presets == null) return;
            
            for (int i = 0; i < presets.Length; i++)
            {
                if (presets[i].name == presetName)
                {
                    ApplyPreset(i);
                    return;
                }
            }
        }
        
        /// <summary>
        /// 随机应用一个预设
        /// </summary>
        public void ApplyRandomPreset()
        {
            if (presets != null && presets.Length > 0)
            {
                int randomIndex = Random.Range(0, presets.Length);
                ApplyPreset(randomIndex);
            }
        }
        
        /// <summary>
        /// 循环切换预设
        /// </summary>
        public void CyclePresets()
        {
            if (presets == null || presets.Length == 0) return;
            
            // 简单的循环逻辑，可以根据需要改进
            int currentIndex = 0; // 这里可以保存当前索引
            currentIndex = (currentIndex + 1) % presets.Length;
            ApplyPreset(currentIndex);
        }
        
        /// <summary>
        /// 获取预设数量
        /// </summary>
        public int GetPresetCount()
        {
            return presets?.Length ?? 0;
        }
        
        /// <summary>
        /// 获取预设名称
        /// </summary>
        public string GetPresetName(int index)
        {
            if (presets != null && index >= 0 && index < presets.Length)
            {
                return presets[index].name;
            }
            return "";
        }
        
        /// <summary>
        /// 获取所有预设名称
        /// </summary>
        public string[] GetAllPresetNames()
        {
            if (presets == null) return new string[0];
            
            string[] names = new string[presets.Length];
            for (int i = 0; i < presets.Length; i++)
            {
                names[i] = presets[i].name;
            }
            return names;
        }
    }
}
