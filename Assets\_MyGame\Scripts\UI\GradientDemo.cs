using UnityEngine;
using UnityEngine.UI;

namespace MyGame.UI
{
    /// <summary>
    /// 渐变演示脚本
    /// 展示如何使用多色动态渐变背景
    /// </summary>
    public class GradientDemo : MonoBehaviour
    {
        [Header("组件引用")]
        [SerializeField] private DynamicGradientController gradientController;
        [SerializeField] private GradientPresetManager presetManager;
        
        [Header("UI控制")]
        [SerializeField] private Button[] presetButtons;
        [SerializeField] private Button randomButton;
        [SerializeField] private Button cycleButton;
        [SerializeField] private Slider speedSlider;
        [SerializeField] private Text currentPresetText;
        
        [Header("自动切换")]
        [SerializeField] private bool autoSwitch = false;
        [SerializeField] private float switchInterval = 5f;
        
        private int currentPresetIndex = 0;
        private float autoSwitchTimer = 0f;
        
        void Start()
        {
            SetupUI();
            
            // 应用默认预设
            if (presetManager != null)
            {
                presetManager.ApplyPreset(0);
                UpdateCurrentPresetText(0);
            }
        }
        
        void Update()
        {
            HandleAutoSwitch();
        }
        
        /// <summary>
        /// 设置UI控件
        /// </summary>
        private void SetupUI()
        {
            // 设置预设按钮
            if (presetButtons != null && presetManager != null)
            {
                for (int i = 0; i < presetButtons.Length && i < presetManager.GetPresetCount(); i++)
                {
                    int index = i; // 闭包变量
                    presetButtons[i].onClick.AddListener(() => ApplyPreset(index));
                    
                    // 设置按钮文本
                    Text buttonText = presetButtons[i].GetComponentInChildren<Text>();
                    if (buttonText != null)
                    {
                        buttonText.text = presetManager.GetPresetName(index);
                    }
                }
            }
            
            // 设置随机按钮
            if (randomButton != null && presetManager != null)
            {
                randomButton.onClick.AddListener(ApplyRandomPreset);
            }
            
            // 设置循环按钮
            if (cycleButton != null)
            {
                cycleButton.onClick.AddListener(CycleToNextPreset);
            }
            
            // 设置速度滑块
            if (speedSlider != null && gradientController != null)
            {
                speedSlider.value = 0.5f;
                speedSlider.onValueChanged.AddListener(OnSpeedChanged);
            }
        }
        
        /// <summary>
        /// 应用指定预设
        /// </summary>
        public void ApplyPreset(int index)
        {
            if (presetManager != null)
            {
                presetManager.ApplyPreset(index);
                currentPresetIndex = index;
                UpdateCurrentPresetText(index);
                autoSwitchTimer = 0f; // 重置自动切换计时器
            }
        }
        
        /// <summary>
        /// 应用随机预设
        /// </summary>
        public void ApplyRandomPreset()
        {
            if (presetManager != null)
            {
                int randomIndex = Random.Range(0, presetManager.GetPresetCount());
                ApplyPreset(randomIndex);
            }
        }
        
        /// <summary>
        /// 循环到下一个预设
        /// </summary>
        public void CycleToNextPreset()
        {
            if (presetManager != null)
            {
                currentPresetIndex = (currentPresetIndex + 1) % presetManager.GetPresetCount();
                ApplyPreset(currentPresetIndex);
            }
        }
        
        /// <summary>
        /// 速度改变回调
        /// </summary>
        private void OnSpeedChanged(float value)
        {
            if (gradientController != null)
            {
                gradientController.SetAnimationSpeed(value * 2f); // 0-2范围
            }
        }
        
        /// <summary>
        /// 更新当前预设文本
        /// </summary>
        private void UpdateCurrentPresetText(int index)
        {
            if (currentPresetText != null && presetManager != null)
            {
                currentPresetText.text = $"当前: {presetManager.GetPresetName(index)}";
            }
        }
        
        /// <summary>
        /// 处理自动切换
        /// </summary>
        private void HandleAutoSwitch()
        {
            if (!autoSwitch || presetManager == null) return;
            
            autoSwitchTimer += Time.deltaTime;
            if (autoSwitchTimer >= switchInterval)
            {
                CycleToNextPreset();
                autoSwitchTimer = 0f;
            }
        }
        
        /// <summary>
        /// 设置自动切换
        /// </summary>
        public void SetAutoSwitch(bool enabled)
        {
            autoSwitch = enabled;
            autoSwitchTimer = 0f;
        }
        
        /// <summary>
        /// 设置切换间隔
        /// </summary>
        public void SetSwitchInterval(float interval)
        {
            switchInterval = Mathf.Max(1f, interval);
        }
        
        /// <summary>
        /// 演示自定义颜色
        /// </summary>
        public void DemoCustomColors()
        {
            if (gradientController == null) return;
            
            // 演示四色渐变
            Color[] colors = {
                new Color(1f, 0.8f, 0.9f, 1f),  // 浅粉
                new Color(0.9f, 0.7f, 1f, 1f),  // 淡紫
                new Color(0.7f, 0.9f, 1f, 1f),  // 浅蓝
                new Color(0.6f, 1f, 0.9f, 1f)   // 浅绿
            };
            
            gradientController.SetGradientColors(colors[0], colors[1], colors[2], colors[3]);
            
            if (currentPresetText != null)
            {
                currentPresetText.text = "当前: 自定义颜色";
            }
        }
    }
}
