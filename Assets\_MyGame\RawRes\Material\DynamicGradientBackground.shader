Shader "Custom/DynamicGradientBackground"
{
    Properties
    {
        _TopColor ("Top Color", Color) = (1, 0.7, 0.8, 1)
        _BottomColor ("Bottom Color", Color) = (0.5, 0.8, 1, 1)
        _Speed ("Animation Speed", Range(0, 2)) = 0.5
        _WaveAmplitude ("Wave Amplitude", Range(0, 0.5)) = 0.1
        _WaveFrequency ("Wave Frequency", Range(1, 10)) = 3
        _NoiseScale ("Noise Scale", Range(0.1, 5)) = 1
        _NoiseStrength ("Noise Strength", Range(0, 0.3)) = 0.05
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType"="Opaque" 
            "Queue"="Background"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        Pass
        {
            Name "DynamicGradient"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct Varyings
            {
                float4 positionHCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 worldPos : TEXCOORD1;
            };
            
            CBUFFER_START(UnityPerMaterial)
                float4 _TopColor;
                float4 _BottomColor;
                float _Speed;
                float _WaveAmplitude;
                float _WaveFrequency;
                float _NoiseScale;
                float _NoiseStrength;
            CBUFFER_END
            
            // 简单噪声函数
            float noise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }
            
            // 平滑噪声
            float smoothNoise(float2 uv)
            {
                float2 i = floor(uv);
                float2 f = frac(uv);
                f = f * f * (3.0 - 2.0 * f);
                
                float a = noise(i);
                float b = noise(i + float2(1.0, 0.0));
                float c = noise(i + float2(0.0, 1.0));
                float d = noise(i + float2(1.0, 1.0));
                
                return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
            }
            
            Varyings vert(Attributes input)
            {
                Varyings output;
                output.positionHCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = input.uv;
                output.worldPos = TransformObjectToWorld(input.positionOS.xyz);
                return output;
            }
            
            half4 frag(Varyings input) : SV_Target
            {
                float2 uv = input.uv;
                float time = _Time.y * _Speed;
                
                // 创建波浪效果
                float wave1 = sin(uv.y * _WaveFrequency + time) * _WaveAmplitude;
                float wave2 = sin(uv.y * _WaveFrequency * 1.5 + time * 1.3) * _WaveAmplitude * 0.5;
                float waveOffset = wave1 + wave2;
                
                // 添加噪声扰动
                float2 noiseUV = uv * _NoiseScale + time * 0.1;
                float noiseValue = smoothNoise(noiseUV) * _NoiseStrength;
                
                // 计算渐变位置，加入波浪和噪声
                float gradientPos = uv.y + waveOffset + noiseValue;
                
                // 添加时间变化的色彩偏移
                float colorShift = sin(time * 0.8) * 0.1;
                gradientPos += colorShift;
                
                // 确保渐变位置在0-1范围内
                gradientPos = saturate(gradientPos);
                
                // 使用平滑步函数创建更自然的渐变
                float smoothGradient = smoothstep(0.0, 1.0, gradientPos);
                
                // 混合颜色
                half4 finalColor = lerp(_BottomColor, _TopColor, smoothGradient);
                
                // 添加微妙的亮度变化
                float brightness = 1.0 + sin(time * 1.2 + uv.x * 2.0) * 0.05;
                finalColor.rgb *= brightness;
                
                return finalColor;
            }
            ENDHLSL
        }
    }
    
    FallBack "Sprites/Default"
}
