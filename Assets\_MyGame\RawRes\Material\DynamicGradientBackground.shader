Shader "Custom/DynamicGradientBackground"
{
    Properties
    {
        _Color1 ("Color 1 (Top)", Color) = (1, 0.7, 0.8, 1)
        _Color2 ("Color 2", Color) = (1, 0.5, 0.7, 1)
        _Color3 ("Color 3", Color) = (0.8, 0.6, 1, 1)
        _Color4 ("Color 4 (Bottom)", Color) = (0.5, 0.8, 1, 1)
        _ColorCount ("Color Count", Range(2, 4)) = 4
        _Speed ("Animation Speed", Range(0, 2)) = 0.5
        _WaveAmplitude ("Wave Amplitude", Range(0, 0.5)) = 0.1
        _WaveFrequency ("Wave Frequency", Range(1, 10)) = 3
        _NoiseScale ("Noise Scale", Range(0.1, 5)) = 1
        _NoiseStrength ("Noise Strength", Range(0, 0.3)) = 0.05
        _ColorShift ("Color Shift Speed", Range(0, 2)) = 0.3
        _BlendSmoothness ("Blend Smoothness", Range(0.1, 1)) = 0.5
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType"="Opaque" 
            "Queue"="Background"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        Pass
        {
            Name "DynamicGradient"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct Varyings
            {
                float4 positionHCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 worldPos : TEXCOORD1;
            };
            
            CBUFFER_START(UnityPerMaterial)
                float4 _Color1;
                float4 _Color2;
                float4 _Color3;
                float4 _Color4;
                float _ColorCount;
                float _Speed;
                float _WaveAmplitude;
                float _WaveFrequency;
                float _NoiseScale;
                float _NoiseStrength;
                float _ColorShift;
                float _BlendSmoothness;
            CBUFFER_END
            
            // 简单噪声函数
            float noise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }
            
            // 平滑噪声
            float smoothNoise(float2 uv)
            {
                float2 i = floor(uv);
                float2 f = frac(uv);
                f = f * f * (3.0 - 2.0 * f);
                
                float a = noise(i);
                float b = noise(i + float2(1.0, 0.0));
                float c = noise(i + float2(0.0, 1.0));
                float d = noise(i + float2(1.0, 1.0));
                
                return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
            }
            
            Varyings vert(Attributes input)
            {
                Varyings output;
                output.positionHCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = input.uv;
                output.worldPos = TransformObjectToWorld(input.positionOS.xyz);
                return output;
            }
            
            // 多色渐变混合函数
            half4 multiColorGradient(float t, float colorCount)
            {
                half4 result;

                if (colorCount <= 2.0)
                {
                    // 双色渐变
                    result = lerp(_Color4, _Color1, t);
                }
                else if (colorCount <= 3.0)
                {
                    // 三色渐变
                    if (t < 0.5)
                    {
                        result = lerp(_Color4, _Color3, t * 2.0);
                    }
                    else
                    {
                        result = lerp(_Color3, _Color1, (t - 0.5) * 2.0);
                    }
                }
                else
                {
                    // 四色渐变
                    if (t < 0.33)
                    {
                        result = lerp(_Color4, _Color3, t * 3.0);
                    }
                    else if (t < 0.66)
                    {
                        result = lerp(_Color3, _Color2, (t - 0.33) * 3.0);
                    }
                    else
                    {
                        result = lerp(_Color2, _Color1, (t - 0.66) * 3.0);
                    }
                }

                return result;
            }

            half4 frag(Varyings input) : SV_Target
            {
                float2 uv = input.uv;
                float time = _Time.y * _Speed;

                // 创建波浪效果
                float wave1 = sin(uv.y * _WaveFrequency + time) * _WaveAmplitude;
                float wave2 = sin(uv.y * _WaveFrequency * 1.5 + time * 1.3) * _WaveAmplitude * 0.5;
                float waveOffset = wave1 + wave2;

                // 添加噪声扰动
                float2 noiseUV = uv * _NoiseScale + time * 0.1;
                float noiseValue = smoothNoise(noiseUV) * _NoiseStrength;

                // 计算渐变位置，加入波浪和噪声
                float gradientPos = uv.y + waveOffset + noiseValue;

                // 添加时间变化的色彩偏移
                float colorShift = sin(time * _ColorShift) * 0.1;
                gradientPos += colorShift;

                // 确保渐变位置在0-1范围内
                gradientPos = saturate(gradientPos);

                // 使用可调节的平滑度
                float smoothGradient = smoothstep(0.0, _BlendSmoothness, gradientPos) *
                                     (1.0 - smoothstep(1.0 - _BlendSmoothness, 1.0, gradientPos)) +
                                     step(_BlendSmoothness, gradientPos) * step(gradientPos, 1.0 - _BlendSmoothness);

                // 应用多色渐变
                half4 finalColor = multiColorGradient(smoothGradient, _ColorCount);

                // 添加微妙的亮度变化
                float brightness = 1.0 + sin(time * 1.2 + uv.x * 2.0) * 0.05;
                finalColor.rgb *= brightness;

                // 添加色彩循环效果
                float colorCycle = sin(time * _ColorShift * 0.5) * 0.1;
                finalColor.rgb += colorCycle;

                return finalColor;
            }
            ENDHLSL
        }
    }
    
    FallBack "Sprites/Default"
}
