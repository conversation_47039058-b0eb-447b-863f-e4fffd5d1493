using UnityEngine;
using UnityEngine.UI;

namespace MyGame.UI
{
    /// <summary>
    /// 动态渐变背景控制器
    /// 用于控制背景的渐变效果和动画参数
    /// </summary>
    public class DynamicGradientController : MonoBehaviour
    {
        [Header("材质设置")]
        [SerializeField] private Material gradientMaterial;
        [SerializeField] private Image backgroundImage;

        [Header("颜色设置")]
        [SerializeField] private Color color1 = new Color(1f, 0.7f, 0.8f, 1f);  // 顶部
        [SerializeField] private Color color2 = new Color(1f, 0.5f, 0.7f, 1f);  // 中上
        [SerializeField] private Color color3 = new Color(0.8f, 0.6f, 1f, 1f);  // 中下
        [SerializeField] private Color color4 = new Color(0.5f, 0.8f, 1f, 1f);  // 底部
        [SerializeField, Range(2, 4)] private int colorCount = 4;

        [Header("动画参数")]
        [SerializeField, Range(0f, 2f)] private float animationSpeed = 0.5f;
        [SerializeField, Range(0f, 0.5f)] private float waveAmplitude = 0.1f;
        [SerializeField, Range(1f, 10f)] private float waveFrequency = 3f;
        [SerializeField, Range(0.1f, 5f)] private float noiseScale = 1f;
        [SerializeField, Range(0f, 0.3f)] private float noiseStrength = 0.05f;
        [SerializeField, Range(0f, 2f)] private float colorShiftSpeed = 0.3f;
        [SerializeField, Range(0.1f, 1f)] private float blendSmoothness = 0.5f;

        [Header("预设配置")]
        [SerializeField] private GradientPreset[] presets;

        private Material materialInstance;

        void Start()
        {
            InitializeMaterial();
            ApplySettings();
        }

        void Update()
        {
            // 实时更新材质参数（仅在编辑器中或需要动态调整时）
            if (Application.isEditor)
            {
                ApplySettings();
            }
        }

        /// <summary>
        /// 初始化材质实例
        /// </summary>
        private void InitializeMaterial()
        {
            if (gradientMaterial != null)
            {
                materialInstance = new Material(gradientMaterial);

                if (backgroundImage != null)
                {
                    backgroundImage.material = materialInstance;
                }
            }
        }

        /// <summary>
        /// 应用当前设置到材质
        /// </summary>
        private void ApplySettings()
        {
            if (materialInstance == null) return;

            materialInstance.SetColor("_Color1", color1);
            materialInstance.SetColor("_Color2", color2);
            materialInstance.SetColor("_Color3", color3);
            materialInstance.SetColor("_Color4", color4);
            materialInstance.SetFloat("_ColorCount", colorCount);
            materialInstance.SetFloat("_Speed", animationSpeed);
            materialInstance.SetFloat("_WaveAmplitude", waveAmplitude);
            materialInstance.SetFloat("_WaveFrequency", waveFrequency);
            materialInstance.SetFloat("_NoiseScale", noiseScale);
            materialInstance.SetFloat("_NoiseStrength", noiseStrength);
            materialInstance.SetFloat("_ColorShift", colorShiftSpeed);
            materialInstance.SetFloat("_BlendSmoothness", blendSmoothness);
        }

        /// <summary>
        /// 设置渐变颜色（双色）
        /// </summary>
        public void SetGradientColors(Color top, Color bottom)
        {
            color1 = top;
            color4 = bottom;
            colorCount = 2;
            ApplySettings();
        }

        /// <summary>
        /// 设置渐变颜色（三色）
        /// </summary>
        public void SetGradientColors(Color top, Color middle, Color bottom)
        {
            color1 = top;
            color3 = middle;
            color4 = bottom;
            colorCount = 3;
            ApplySettings();
        }

        /// <summary>
        /// 设置渐变颜色（四色）
        /// </summary>
        public void SetGradientColors(Color top, Color upperMiddle, Color lowerMiddle, Color bottom)
        {
            color1 = top;
            color2 = upperMiddle;
            color3 = lowerMiddle;
            color4 = bottom;
            colorCount = 4;
            ApplySettings();
        }

        /// <summary>
        /// 设置颜色数量
        /// </summary>
        public void SetColorCount(int count)
        {
            colorCount = Mathf.Clamp(count, 2, 4);
            ApplySettings();
        }

        /// <summary>
        /// 设置动画速度
        /// </summary>
        public void SetAnimationSpeed(float speed)
        {
            animationSpeed = Mathf.Clamp(speed, 0f, 2f);
            ApplySettings();
        }

        /// <summary>
        /// 应用预设配置
        /// </summary>
        public void ApplyPreset(int presetIndex)
        {
            if (presets != null && presetIndex >= 0 && presetIndex < presets.Length)
            {
                var preset = presets[presetIndex];
                color1 = preset.color1;
                color2 = preset.color2;
                color3 = preset.color3;
                color4 = preset.color4;
                colorCount = preset.colorCount;
                animationSpeed = preset.animationSpeed;
                waveAmplitude = preset.waveAmplitude;
                waveFrequency = preset.waveFrequency;
                noiseScale = preset.noiseScale;
                noiseStrength = preset.noiseStrength;
                colorShiftSpeed = preset.colorShiftSpeed;
                blendSmoothness = preset.blendSmoothness;

                ApplySettings();
            }
        }

        /// <summary>
        /// 平滑过渡到新的颜色（双色）
        /// </summary>
        public void TransitionToColors(Color newTop, Color newBottom, float duration = 1f)
        {
            StartCoroutine(ColorTransitionCoroutine(newTop, Color.white, Color.white, newBottom, 2, duration));
        }

        /// <summary>
        /// 平滑过渡到新的颜色（四色）
        /// </summary>
        public void TransitionToColors(Color newColor1, Color newColor2, Color newColor3, Color newColor4, float duration = 1f)
        {
            StartCoroutine(ColorTransitionCoroutine(newColor1, newColor2, newColor3, newColor4, 4, duration));
        }

        private System.Collections.IEnumerator ColorTransitionCoroutine(Color target1, Color target2, Color target3, Color target4, int targetColorCount, float duration)
        {
            Color start1 = color1;
            Color start2 = color2;
            Color start3 = color3;
            Color start4 = color4;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;

                color1 = Color.Lerp(start1, target1, t);
                color2 = Color.Lerp(start2, target2, t);
                color3 = Color.Lerp(start3, target3, t);
                color4 = Color.Lerp(start4, target4, t);
                colorCount = targetColorCount;

                ApplySettings();
                yield return null;
            }

            color1 = target1;
            color2 = target2;
            color3 = target3;
            color4 = target4;
            colorCount = targetColorCount;
            ApplySettings();
        }

        void OnDestroy()
        {
            if (materialInstance != null)
            {
                DestroyImmediate(materialInstance);
            }
        }
    }

    /// <summary>
    /// 渐变预设配置
    /// </summary>
    [System.Serializable]
    public class GradientPreset
    {
        [Header("预设信息")]
        public string name;

        [Header("颜色设置")]
        public Color color1 = new Color(1f, 0.7f, 0.8f, 1f);
        public Color color2 = new Color(1f, 0.5f, 0.7f, 1f);
        public Color color3 = new Color(0.8f, 0.6f, 1f, 1f);
        public Color color4 = new Color(0.5f, 0.8f, 1f, 1f);
        [Range(2, 4)]
        public int colorCount = 4;

        [Header("动画参数")]
        [Range(0f, 2f)]
        public float animationSpeed = 0.5f;
        [Range(0f, 0.5f)]
        public float waveAmplitude = 0.1f;
        [Range(1f, 10f)]
        public float waveFrequency = 3f;
        [Range(0.1f, 5f)]
        public float noiseScale = 1f;
        [Range(0f, 0.3f)]
        public float noiseStrength = 0.05f;
        [Range(0f, 2f)]
        public float colorShiftSpeed = 0.3f;
        [Range(0.1f, 1f)]
        public float blendSmoothness = 0.5f;
    }
}
