using UnityEngine;
using UnityEngine.UI;

namespace MyGame.UI
{
    /// <summary>
    /// 动态渐变背景控制器
    /// 用于控制背景的渐变效果和动画参数
    /// </summary>
    public class DynamicGradientController : MonoBehaviour
    {
        [Header("材质设置")]
        [SerializeField] private Material gradientMaterial;
        [SerializeField] private Image backgroundImage;
        
        [Header("颜色设置")]
        [SerializeField] private Color topColor = new Color(1f, 0.7f, 0.8f, 1f);
        [SerializeField] private Color bottomColor = new Color(0.5f, 0.8f, 1f, 1f);
        
        [Header("动画参数")]
        [SerializeField, Range(0f, 2f)] private float animationSpeed = 0.5f;
        [SerializeField, Range(0f, 0.5f)] private float waveAmplitude = 0.1f;
        [SerializeField, Range(1f, 10f)] private float waveFrequency = 3f;
        [SerializeField, Range(0.1f, 5f)] private float noiseScale = 1f;
        [SerializeField, Range(0f, 0.3f)] private float noiseStrength = 0.05f;
        
        [Header("预设配置")]
        [SerializeField] private GradientPreset[] presets;
        
        private Material materialInstance;
        
        void Start()
        {
            InitializeMaterial();
            ApplySettings();
        }
        
        void Update()
        {
            // 实时更新材质参数（仅在编辑器中或需要动态调整时）
            if (Application.isEditor)
            {
                ApplySettings();
            }
        }
        
        /// <summary>
        /// 初始化材质实例
        /// </summary>
        private void InitializeMaterial()
        {
            if (gradientMaterial != null)
            {
                materialInstance = new Material(gradientMaterial);
                
                if (backgroundImage != null)
                {
                    backgroundImage.material = materialInstance;
                }
            }
        }
        
        /// <summary>
        /// 应用当前设置到材质
        /// </summary>
        private void ApplySettings()
        {
            if (materialInstance == null) return;
            
            materialInstance.SetColor("_TopColor", topColor);
            materialInstance.SetColor("_BottomColor", bottomColor);
            materialInstance.SetFloat("_Speed", animationSpeed);
            materialInstance.SetFloat("_WaveAmplitude", waveAmplitude);
            materialInstance.SetFloat("_WaveFrequency", waveFrequency);
            materialInstance.SetFloat("_NoiseScale", noiseScale);
            materialInstance.SetFloat("_NoiseStrength", noiseStrength);
        }
        
        /// <summary>
        /// 设置渐变颜色
        /// </summary>
        public void SetGradientColors(Color top, Color bottom)
        {
            topColor = top;
            bottomColor = bottom;
            ApplySettings();
        }
        
        /// <summary>
        /// 设置动画速度
        /// </summary>
        public void SetAnimationSpeed(float speed)
        {
            animationSpeed = Mathf.Clamp(speed, 0f, 2f);
            ApplySettings();
        }
        
        /// <summary>
        /// 应用预设配置
        /// </summary>
        public void ApplyPreset(int presetIndex)
        {
            if (presets != null && presetIndex >= 0 && presetIndex < presets.Length)
            {
                var preset = presets[presetIndex];
                topColor = preset.topColor;
                bottomColor = preset.bottomColor;
                animationSpeed = preset.animationSpeed;
                waveAmplitude = preset.waveAmplitude;
                waveFrequency = preset.waveFrequency;
                noiseScale = preset.noiseScale;
                noiseStrength = preset.noiseStrength;
                
                ApplySettings();
            }
        }
        
        /// <summary>
        /// 平滑过渡到新的颜色
        /// </summary>
        public void TransitionToColors(Color newTop, Color newBottom, float duration = 1f)
        {
            StartCoroutine(ColorTransitionCoroutine(newTop, newBottom, duration));
        }
        
        private System.Collections.IEnumerator ColorTransitionCoroutine(Color targetTop, Color targetBottom, float duration)
        {
            Color startTop = topColor;
            Color startBottom = bottomColor;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                
                topColor = Color.Lerp(startTop, targetTop, t);
                bottomColor = Color.Lerp(startBottom, targetBottom, t);
                
                ApplySettings();
                yield return null;
            }
            
            topColor = targetTop;
            bottomColor = targetBottom;
            ApplySettings();
        }
        
        void OnDestroy()
        {
            if (materialInstance != null)
            {
                DestroyImmediate(materialInstance);
            }
        }
    }
    
    /// <summary>
    /// 渐变预设配置
    /// </summary>
    [System.Serializable]
    public class GradientPreset
    {
        public string name;
        public Color topColor;
        public Color bottomColor;
        public float animationSpeed = 0.5f;
        public float waveAmplitude = 0.1f;
        public float waveFrequency = 3f;
        public float noiseScale = 1f;
        public float noiseStrength = 0.05f;
    }
}
